using Godot;

public partial class Shop : Node2D
{
	private Area2D _playerCollider;
	private bool _isPlayerInRange = false;
	private ShopMenu _shopMenu;

	public override void _Ready()
	{
		_playerCollider = GetNode<Area2D>("PlayerCollider");
		if (_playerCollider == null)
		{
			GD.PrintErr("Shop: PlayerCollider not found!");
			return;
		}

		_playerCollider.CollisionMask = 4;
		_playerCollider.AreaEntered += OnPlayerEntered;
		_playerCollider.AreaExited += OnPlayerExited;

		_shopMenu = GetNode<ShopMenu>("ShopMenu");
		if (_shopMenu == null)
		{
			GD.PrintErr("Shop: ShopMenu not found in world scene!");
		}
	}

	public override void _ExitTree()
	{
		if (_playerCollider != null)
		{
			_playerCollider.AreaEntered -= OnPlayerEntered;
			_playerCollider.AreaExited -= OnPlayerExited;
		}
	}

	public override void _Input(InputEvent @event)
	{
		if (!_isPlayerInRange) return;

		if (@event is InputEventKey keyEvent && keyEvent.Pressed)
		{
			if (keyEvent.Keycode == Key.R)
			{
				OpenShop();
			}
		}
	}

	private void OnPlayerEntered(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			_isPlayerInRange = true;
			GD.Print("Shop: Player in range - press 'R' to open shop");
		}
	}

	private void OnPlayerExited(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			_isPlayerInRange = false;
		}
	}

	private void OpenShop()
	{
		if (_shopMenu != null)
		{
			_shopMenu.OpenShop();
		}
	}
}
