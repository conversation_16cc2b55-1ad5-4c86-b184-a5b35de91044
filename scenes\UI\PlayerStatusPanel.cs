using Godot;
using System;

public partial class PlayerStatusPanel : CanvasLayer
{
	private ProgressBar _healthBar;
	private ProgressBar _energyBar;
	private ProgressBar _foodBar;
	private ProgressBar _waterBar;
	private ProgressBar _levelBar;
	private Label _goldLabel;
	private Label _levelLabel;
	private Label _percentageLabel;

	public override void _Ready()
	{
		GetNodes();
		ConnectSignals();
		UpdateAllBars();
	}

	private void GetNodes()
	{
		_healthBar = GetNode<ProgressBar>("TopLeftControl/Node2D/ProgressBarHealth");
		_energyBar = GetNode<ProgressBar>("TopLeftControl/Node2D/ProgressBarEnergy");
		_foodBar = GetNode<ProgressBar>("TopLeftControl/Node2D/ProgressBarFood");
		_waterBar = GetNode<ProgressBar>("TopLeftControl/Node2D/ProgressBarWater");
		_levelBar = GetNode<ProgressBar>("TopCenterControl/ProgressPanel/ProgressBarLevel");
		_goldLabel = GetNode<Label>("TopLeftControl/Node2D/GoldLabel");
		_levelLabel = GetNode<Label>("TopCenterControl/Level");
		_percentageLabel = GetNode<Label>("TopCenterControl/Percentage");
	}

	private void ConnectSignals()
	{
		if (ResourcesManager.Instance != null)
		{
			ResourcesManager.Instance.StatChanged += OnStatChanged;
		}

		if (PlayerStatsManager.Instance != null)
		{
			PlayerStatsManager.Instance.EnergyChanged += OnEnergyChanged;
			PlayerStatsManager.Instance.FoodChanged += OnFoodChanged;
			PlayerStatsManager.Instance.WaterChanged += OnWaterChanged;
			PlayerStatsManager.Instance.HealthChanged += OnHealthChanged;
		}
	}

	private void OnStatChanged(object sender, StatChangedEventArgs e)
	{
		switch (e.StatName)
		{
			case "Money":
				UpdateGoldLabel();
				break;
			case "Level":
			case "Experience":
				UpdateLevelBar();
				break;
		}
	}

	private void OnEnergyChanged(int current, int max)
	{
		if (_energyBar != null)
		{
			float percentage = (float)current / max;
			_energyBar.SetProgress(percentage);
		}
	}

	private void OnFoodChanged(int current, int max)
	{
		if (_foodBar != null)
		{
			float percentage = (float)current / max;
			_foodBar.SetProgress(percentage);
		}
	}

	private void OnWaterChanged(int current, int max)
	{
		if (_waterBar != null)
		{
			float percentage = (float)current / max;
			_waterBar.SetProgress(percentage);
		}
	}

	private void OnHealthChanged(int current, int max)
	{
		if (_healthBar != null)
		{
			float percentage = (float)current / max;
			_healthBar.SetProgress(percentage);
		}
	}

	private void UpdateAllBars()
	{
		UpdateHealthBarFromManager();
		UpdateEnergyBarFromManager();
		UpdateFoodBarFromManager();
		UpdateWaterBarFromManager();
		UpdateLevelBar();
		UpdateGoldLabel();
	}

	private void UpdateHealthBarFromManager()
	{
		if (_healthBar == null || PlayerStatsManager.Instance == null) return;

		var psm = PlayerStatsManager.Instance;
		float healthPercentage = (float)psm.CurrentHealth / psm.MaxHealth;
		_healthBar.SetProgress(healthPercentage);
	}

	private void UpdateEnergyBarFromManager()
	{
		if (_energyBar == null || PlayerStatsManager.Instance == null) return;

		var psm = PlayerStatsManager.Instance;
		float energyPercentage = (float)psm.CurrentEnergy / psm.MaxEnergy;
		_energyBar.SetProgress(energyPercentage);
	}

	private void UpdateFoodBarFromManager()
	{
		if (_foodBar == null || PlayerStatsManager.Instance == null) return;

		var psm = PlayerStatsManager.Instance;
		float foodPercentage = (float)psm.CurrentFood / psm.MaxFood;
		_foodBar.SetProgress(foodPercentage);
	}

	private void UpdateWaterBarFromManager()
	{
		if (_waterBar == null || PlayerStatsManager.Instance == null) return;

		var psm = PlayerStatsManager.Instance;
		float waterPercentage = (float)psm.CurrentWater / psm.MaxWater;
		_waterBar.SetProgress(waterPercentage);
	}

	private void UpdateLevelBar()
	{
		if (_levelBar == null || ResourcesManager.Instance == null) return;

		var rm = ResourcesManager.Instance;
		int currentLevel = GameSaveData.Instance.PlayerStats.Level;
		int currentXp = GameSaveData.Instance.PlayerStats.Experience;

		_levelLabel.Text = $"LVL {currentLevel}";

		if (currentLevel >= 50)
		{
			_levelBar.SetProgress(1.0f);
			_percentageLabel.Text = "MAX";
		}
		else
		{
			float progress = LevelManager.GetLevelProgress(currentLevel, currentXp);
			_levelBar.SetProgress(progress);
			_percentageLabel.Text = $"{(progress * 100):F0}%";
		}
	}

	private void UpdateGoldLabel()
	{
		if (_goldLabel == null || ResourcesManager.Instance == null) return;

		int money = GameSaveData.Instance.PlayerStats.Money;
		_goldLabel.Text = money.ToString();
	}

	public override void _ExitTree()
	{
		if (ResourcesManager.Instance != null)
		{
			ResourcesManager.Instance.StatChanged -= OnStatChanged;
		}
	}
}
