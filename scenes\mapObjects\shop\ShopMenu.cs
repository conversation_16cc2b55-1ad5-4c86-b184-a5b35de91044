using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

public partial class ShopMenu : CanvasLayer
{
	private AnimationPlayer _animationPlayer;
	private Button _closeButton;
	private List<MenuSelectionSector> _selectionSectors = new List<MenuSelectionSector>();
	
	private Sprite2D _infoBoard;
	private Sprite2D _itemFront;
	private Label _amountToProduce;
	private Button _buttonMinusOne;
	private Button _buttonPlusOne;
	private Button _buttonSetOne;
	private Button _buttonSet25Percent;
	private Button _buttonSet50Percent;
	private Button _buttonSetMax;
	private Button _buttonSell;
	private Label _sellPrice;
	
	private Node2D[] _shopItems = new Node2D[4];
	private Sprite2D[] _shopItemForegrounds = new Sprite2D[4];
	private Label[] _shopItemPrices = new Label[4];
	private Label[] _shopItemAmounts = new Label[4];
	
	private ResourceType _selectedResourceType = ResourceType.None;
	private int _selectedResourceQuantity = 0;
	private int _amountToSell = 1;
	
	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		
		InitializeSelectionSectors();
		InitializeInfoBoard();
		InitializeShopItems();
		
		_closeButton.Pressed += OnCloseButtonPressed;
		
		GetNode<Sprite2D>("Control/Panel").Visible = false;
		
		RefreshDailyShopItems();
	}
	
	private void InitializeSelectionSectors()
	{
		var panel = GetNode<Sprite2D>("Control/Panel");
		
		foreach (Node child in panel.GetChildren())
		{
			if (child is MenuSelectionSector sector)
			{
				_selectionSectors.Add(sector);
				sector.ItemSelected += OnSectorItemSelected;
			}
		}
		
		_selectionSectors.Sort((a, b) => a.Name.ToString().CompareTo(b.Name.ToString()));
	}
	
	private void InitializeInfoBoard()
	{
		_infoBoard = GetNode<Sprite2D>("Control/Panel/InfoBoard");
		_itemFront = GetNode<Sprite2D>("Control/Panel/InfoBoard/ItemFront");
		_amountToProduce = GetNode<Label>("Control/Panel/InfoBoard/AmountToProduce");
		_sellPrice = GetNode<Label>("Control/Panel/InfoBoard/SellButton/SellPrice");
		
		_buttonMinusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonMinusOne");
		_buttonPlusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonPlusOne");
		_buttonSetOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetOne");
		_buttonSet25Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet25Percent");
		_buttonSet50Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet50Percent");
		_buttonSetMax = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetMax");
		_buttonSell = GetNode<Button>("Control/Panel/InfoBoard/ButtonSell");
		
		_buttonMinusOne.Pressed += () => ChangeAmount(-1);
		_buttonPlusOne.Pressed += () => ChangeAmount(1);
		_buttonSetOne.Pressed += () => SetAmount(1);
		_buttonSet25Percent.Pressed += () => SetAmountPercent(0.25f);
		_buttonSet50Percent.Pressed += () => SetAmountPercent(0.5f);
		_buttonSetMax.Pressed += () => SetAmountPercent(1.0f);
		_buttonSell.Pressed += OnSellButtonPressed;
		
		ClearInfoBoard();
	}
	
	private void InitializeShopItems()
	{
		var shopMenu = GetNode<Sprite2D>("Control/Panel/ShopMenu");

		for (int i = 0; i < 4; i++)
		{
			_shopItems[i] = GetNode<Node2D>($"Control/Panel/ShopMenu/Item{i + 1}");
			_shopItemForegrounds[i] = GetNode<Sprite2D>($"Control/Panel/ShopMenu/Item{i + 1}/ItemForeground");
			_shopItemPrices[i] = GetNode<Label>($"Control/Panel/ShopMenu/Item{i + 1}/Price");
			_shopItemAmounts[i] = GetNode<Label>($"Control/Panel/ShopMenu/Item{i + 1}/AvailableAmount");

			int itemIndex = i;
			var button = GetNode<Button>($"Control/Panel/ShopMenu/Item{i + 1}/ItemButton");
			button.Pressed += () => OnShopItemClicked(itemIndex);
		}
	}
	
	private void OnSectorItemSelected(MenuSelectionSector sector, int slotIndex, ResourceType resourceType, int quantity)
	{
		HideAllPlaceholders();

		_selectedResourceType = resourceType;
		_selectedResourceQuantity = quantity;
		_amountToSell = 1;

		UpdateInfoBoard();
	}
	
	private void UpdateInfoBoard()
	{
		if (_selectedResourceType == ResourceType.None)
		{
			ClearInfoBoard();
			return;
		}
		
		var texture = TextureManager.Instance?.GetResourceIconTexture(_selectedResourceType);
		_itemFront.Texture = texture;
		
		_amountToProduce.Text = _amountToSell.ToString();
		
		var itemInfo = ItemInformation.GetResourceInfo(_selectedResourceType);
		int totalSellPrice = itemInfo.SellPrice * _amountToSell;
		_sellPrice.Text = totalSellPrice.ToString();
	}
	
	private void ClearInfoBoard()
	{
		_itemFront.Texture = null;
		_amountToProduce.Text = "0";
		_sellPrice.Text = "0";
		_selectedResourceType = ResourceType.None;
		_selectedResourceQuantity = 0;
		_amountToSell = 1;
	}
	
	private void ChangeAmount(int delta)
	{
		int newAmount = _amountToSell + delta;
		newAmount = Mathf.Clamp(newAmount, 1, _selectedResourceQuantity);
		_amountToSell = newAmount;
		UpdateInfoBoard();
	}
	
	private void SetAmount(int amount)
	{
		_amountToSell = Mathf.Clamp(amount, 1, _selectedResourceQuantity);
		UpdateInfoBoard();
	}
	
	private void SetAmountPercent(float percent)
	{
		int amount = Mathf.Max(1, Mathf.RoundToInt(_selectedResourceQuantity * percent));
		SetAmount(amount);
	}
	
	private void OnSellButtonPressed()
	{
		if (_selectedResourceType == ResourceType.None || _amountToSell <= 0)
			return;
		
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;
		
		if (!resourcesManager.HasResource(_selectedResourceType, _amountToSell))
		{
			GD.Print("Shop: Not enough resources to sell");
			return;
		}
		
		var itemInfo = ItemInformation.GetResourceInfo(_selectedResourceType);
		int totalSellPrice = itemInfo.SellPrice * _amountToSell;
		
		if (resourcesManager.RemoveResource(_selectedResourceType, _amountToSell))
		{
			GameSaveData.Instance.PlayerStats.Money += totalSellPrice;
			resourcesManager.EmitStatChanged("Money", GameSaveData.Instance.PlayerStats.Money);
			GD.Print($"Shop: Sold {_amountToSell}x {_selectedResourceType} for {totalSellPrice} coins");

			RefreshInventoryDisplay();
			ClearInfoBoard();
		}
	}
	
	public void RefreshInventoryDisplay()
	{
		var resources = GameSaveData.Instance.PlayerResources.Resources;
		var maxSlots = GameSaveData.Instance.PlayerStats.MaxInventorySlots;
		
		int slotsPerSector = 4;
		int requiredSectors = Mathf.CeilToInt((float)maxSlots / slotsPerSector);
		
		var resourcesList = resources.Where(kvp => IsResourceType(kvp.Key)).ToList();
		
		for (int sectorIndex = 0; sectorIndex < _selectionSectors.Count && sectorIndex < requiredSectors; sectorIndex++)
		{
			var sectorResources = new Dictionary<int, (ResourceType, int)>();
			
			for (int slotIndex = 0; slotIndex < slotsPerSector; slotIndex++)
			{
				int globalIndex = sectorIndex * slotsPerSector + slotIndex;
				if (globalIndex < resourcesList.Count)
				{
					var resource = resourcesList[globalIndex];
					sectorResources[slotIndex] = (resource.Key, resource.Value);
				}
			}
			
			_selectionSectors[sectorIndex].UpdateInventoryDisplay(sectorResources);
		}
	}
	
	private bool IsResourceType(ResourceType resourceType)
	{
		return resourceType != ResourceType.None;
	}
	
	public void RefreshDailyShopItems()
	{
		var shopData = GameSaveData.Instance.ShopData;
		var currentDate = DateTime.Now.Date;
		
		if (shopData.LastRefreshDate.Date != currentDate)
		{
			GenerateNewDailyItems();
			shopData.LastRefreshDate = currentDate;
		}
		
		UpdateShopItemsDisplay();
	}
	
	private void GenerateNewDailyItems()
	{
		var shopData = GameSaveData.Instance.ShopData;
		shopData.DailyItems.Clear();
		
		var random = new Random();
		var availableItems = shopData.AvailableItemsPool.ToList();
		
		for (int i = 0; i < 4 && availableItems.Count > 0; i++)
		{
			int randomIndex = random.Next(availableItems.Count);
			var selectedItem = availableItems[randomIndex];
			availableItems.RemoveAt(randomIndex);
			
			int randomAmount = random.Next(1, 101);
			int finalAmount = Mathf.Max(1, Mathf.RoundToInt((float)randomAmount / selectedItem.Weight));
			
			shopData.DailyItems.Add(new ShopItem
			{
				ResourceType = selectedItem.ResourceType,
				Weight = selectedItem.Weight,
				AvailableAmount = finalAmount
			});
		}
	}
	
	private void UpdateShopItemsDisplay()
	{
		var dailyItems = GameSaveData.Instance.ShopData.DailyItems;
		
		for (int i = 0; i < 4; i++)
		{
			if (i < dailyItems.Count)
			{
				var item = dailyItems[i];
				var texture = TextureManager.Instance?.GetResourceIconTexture(item.ResourceType);
				var itemInfo = ItemInformation.GetResourceInfo(item.ResourceType);
				
				_shopItemForegrounds[i].Texture = texture;
				_shopItemPrices[i].Text = itemInfo.BuyPrice.ToString();
				_shopItemAmounts[i].Text = $"x{item.AvailableAmount}";
			}
			else
			{
				_shopItemForegrounds[i].Texture = null;
				_shopItemPrices[i].Text = "0";
				_shopItemAmounts[i].Text = "x0";
			}
		}
	}
	
	public void OpenShop()
	{
		GetNode<Sprite2D>("Control/Panel").Visible = true;
		RefreshInventoryDisplay();
		RefreshDailyShopItems();
		_animationPlayer.Play("Open");
		
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);
	}
	
	public void CloseShop()
	{
		HideAllPlaceholders();
		ClearInfoBoard();
		_animationPlayer.Play("Close");

		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);
	}
	
	private void OnCloseButtonPressed()
	{
		CloseShop();
	}

	private void HideAllPlaceholders()
	{
		foreach (var sector in _selectionSectors)
		{
			sector.HideAllPlaceholders();
		}
	}

	private void OnShopItemClicked(int itemIndex)
	{
		var dailyItems = GameSaveData.Instance.ShopData.DailyItems;
		if (itemIndex >= dailyItems.Count) return;

		var item = dailyItems[itemIndex];
		if (item.AvailableAmount <= 0) return;

		var itemInfo = ItemInformation.GetResourceInfo(item.ResourceType);
		int buyPrice = itemInfo.BuyPrice;

		if (GameSaveData.Instance.PlayerStats.Money < buyPrice)
		{
			GD.Print("Shop: Not enough money to buy this item");
			return;
		}

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		GameSaveData.Instance.PlayerStats.Money -= buyPrice;
		resourcesManager.EmitStatChanged("Money", GameSaveData.Instance.PlayerStats.Money);
		resourcesManager.AddResource(item.ResourceType, 1);
		item.AvailableAmount--;

		GD.Print($"Shop: Bought 1x {item.ResourceType} for {buyPrice} coins");

		UpdateShopItemsDisplay();
		RefreshInventoryDisplay();
	}
}
