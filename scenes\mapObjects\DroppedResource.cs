using Godot;
using System;

public partial class DroppedResource : Node2D
{
	[Export] public ResourceType ResourceType { get; set; } = ResourceType.Wood;
	[Export] public int Quantity { get; set; } = 1;
	[Export] public float CollectionRange { get; set; } = 8.0f;
	[Export] public float DropAnimationDuration { get; set; } = 0.75f;
	[Export] public float CollectionAnimationDuration { get; set; } = 0.5f;

	public string Id { get; set; } = System.Guid.NewGuid().ToString();

	private Sprite2D _sprite;
	private Area2D _collectionArea;
	private CollisionShape2D _collisionShape;
	private Tween _animationTween;
	private bool _isBeingCollected = false;
	private bool _canBeCollected = true;

	private Vector2 _startPosition;
	private Vector2 _targetPosition;

	public override void _Ready()
	{
		_sprite = GetNode<Sprite2D>("Sprite2D");
		_collectionArea = GetNode<Area2D>("CollectionArea");
		_collisionShape = GetNode<CollisionShape2D>("CollectionArea/CollisionShape2D");

		if (_sprite == null || _collectionArea == null || _collisionShape == null)
		{
			GD.PrintErr("DroppedResource: Required child nodes not found!");
			return;
		}

		YSortEnabled = true;
		AddToGroup("dropped_resources");
		SetupCollectionArea();
		SetResourceTexture();
		_collectionArea.AreaEntered += OnAreaEntered;
		_collectionArea.AreaExited += OnAreaExited;
		StartDropAnimation();
	}

	private void SetupCollectionArea()
	{
		var circleShape = new CircleShape2D();
		circleShape.Radius = CollectionRange;
		_collisionShape.Shape = circleShape;

		// Set collision layer and mask for Area2D detection
		// Layer 2: DroppedResource detection layer
		// Mask 3: Detect PlayerDetector layer
		_collectionArea.CollisionLayer = 2; // This Area2D is on layer 2
		_collectionArea.CollisionMask = 4;  // This Area2D detects layer 3 (PlayerDetector)
	}

	private void SetResourceTexture()
	{
		if (TextureManager.Instance != null)
		{
			var iconTexture = TextureManager.Instance.GetResourceIconTexture(ResourceType);
			if (iconTexture != null)
			{
				_sprite.Texture = iconTexture;
			}
			else
			{
				var regularTexture = TextureManager.Instance.GetResourceTexture(ResourceType);
				if (regularTexture != null)
				{
					_sprite.Texture = regularTexture;
					GD.Print($"DroppedResource: Using regular texture for {ResourceType} (icon texture not set)");
				}
				else
				{
					GD.PrintErr($"DroppedResource: No texture or icon texture found for resource type {ResourceType}");
				}
			}
		}
		else
		{
			GD.PrintErr("DroppedResource: TextureManager instance not found!");
		}
	}

	private void StartDropAnimation()
	{
		_startPosition = GlobalPosition;

		var random = new Random();
		var angle = random.NextDouble() * Math.PI * 2;
		var distance = random.NextDouble() * 8.0;

		_targetPosition = _startPosition + new Vector2(
			(float)(Math.Cos(angle) * distance),
			(float)(Math.Sin(angle) * distance)
		);

		_animationTween = CreateTween();
		_animationTween.SetParallel(true);

		var midPosition = _startPosition + new Vector2(0, -16);

		var halfwayPosition = (_startPosition + midPosition) * 0.5f + (_targetPosition - _startPosition) * 0.3f;
		_animationTween.TweenProperty(this, "global_position", halfwayPosition, DropAnimationDuration * 0.4f)
			.SetEase(Tween.EaseType.Out);

		_animationTween.TweenProperty(this, "global_position", _targetPosition, DropAnimationDuration * 0.6f)
			.SetDelay(DropAnimationDuration * 0.4f)
			.SetEase(Tween.EaseType.In);

		var rotationAmount = (float)(random.NextDouble() - 0.5) * 0.5f;
		_animationTween.TweenProperty(_sprite, "rotation", rotationAmount, DropAnimationDuration)
			.SetEase(Tween.EaseType.Out);

		_sprite.Scale = Vector2.Zero;
		_animationTween.TweenProperty(_sprite, "scale", Vector2.One, DropAnimationDuration * 0.3f)
			.SetEase(Tween.EaseType.Out)
			.SetTrans(Tween.TransitionType.Bounce);

		_animationTween.TweenCallback(Callable.From(EnableCollection))
			.SetDelay(DropAnimationDuration);
	}

	private void EnableCollection()
	{
		_canBeCollected = true;
		CheckForPlayerInRange();
	}

	private void CheckForPlayerInRange()
	{
		if (!_canBeCollected || _isBeingCollected) return;

		var player = GetNode<PlayerController>("/root/world/Player");
		if (player == null) return;

		float distanceToPlayer = GlobalPosition.DistanceTo(player.GlobalPosition);
		if (distanceToPlayer <= CollectionRange)
		{
			StartCollection(player);
		}
	}

	private void OnAreaEntered(Area2D area)
	{
		if (!_canBeCollected || _isBeingCollected) return;

		// Check if this is the PlayerDetector Area2D
		if (area.Name == "PlayerDetector" && area.GetParent() is PlayerController player)
		{
			// Check if player can collect this resource
			if (CanPlayerCollectResource())
			{
				StartCollection(player);
			}
		}
	}

	private void OnAreaExited(Area2D area)
	{
	}

	private void StartCollection(PlayerController player)
	{
		if (_isBeingCollected) return;

		_isBeingCollected = true;

		_animationTween?.Kill();
		_animationTween = CreateTween();

		_animationTween.TweenProperty(_sprite, "scale", Vector2.Zero, 0.2f)
			.SetEase(Tween.EaseType.In);

		_animationTween.TweenCallback(Callable.From(() => CompleteCollection()))
			.SetDelay(0.2f);
	}

	private void CompleteCollection()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager != null)
		{
			// Try to add the resource to inventory
			bool success = resourcesManager.AddResource(ResourceType, Quantity);
			if (success)
			{
				// Remove from GameData directly
				RemoveFromGameData();
				QueueFree();
			}
			else
			{
				// If inventory is full, reset collection state so player can try again later
				_isBeingCollected = false;
				GD.Print($"Inventory full! Cannot collect {Quantity}x {ResourceType}");
			}
		}
		else
		{
			QueueFree();
		}
	}

	private void AddToGameData()
	{
		var droppedResource = new DroppedResourceData
		{
			Id = Id,
			X = GlobalPosition.X,
			Y = GlobalPosition.Y,
			ResourceType = ResourceType,
			Quantity = Quantity
		};
		GameSaveData.Instance.WorldData.DroppedResources.Add(droppedResource);
		GD.Print($"DroppedResource: Added {Quantity}x {ResourceType} to GameData with ID {Id}");
	}

	private void RemoveFromGameData()
	{
		int removedCount = GameSaveData.Instance.WorldData.DroppedResources.RemoveAll(dr => dr.Id == Id);
		GD.Print($"DroppedResource: Removed {Quantity}x {ResourceType} from GameData (removed: {removedCount})");
	}

	/// <summary>
	/// Check if this resource can be collected by the player
	/// </summary>
	public bool CanBeCollected()
	{
		return _canBeCollected && !_isBeingCollected;
	}

	/// <summary>
	/// Check if the player can collect this resource (inventory not full)
	/// </summary>
	private bool CanPlayerCollectResource()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		// Check if the player can add this resource to their inventory
		return resourcesManager.CanAddResource(ResourceType, Quantity);
	}



	public static DroppedResource SpawnResource(Vector2 position, ResourceType resourceType, int quantity = 1)
	{
		var droppedResourceScene = GD.Load<PackedScene>("res://scenes/mapObjects/DroppedResource.tscn");
		if (droppedResourceScene == null)
		{
			GD.PrintErr("DroppedResource: Could not load DroppedResource.tscn");
			return null;
		}

		var droppedResource = droppedResourceScene.Instantiate<DroppedResource>();
		if (droppedResource == null)
		{
			GD.PrintErr("DroppedResource: Failed to instantiate DroppedResource");
			return null;
		}

		droppedResource.GlobalPosition = position;
		droppedResource.ResourceType = resourceType;
		droppedResource.Quantity = quantity;

		var currentScene = Engine.GetMainLoop() as SceneTree;
		currentScene?.CurrentScene?.CallDeferred(Node.MethodName.AddChild, droppedResource);

		// Add to GameData after the resource is properly initialized
		droppedResource.CallDeferred(nameof(droppedResource.AddToGameData));

		return droppedResource;
	}
}
