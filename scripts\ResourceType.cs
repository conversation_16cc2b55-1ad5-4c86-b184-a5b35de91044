using Godot;

[System.Serializable]
public enum ResourceType
{
	None = 0,
	<PERSON> = 1,
	<PERSON> = 2,
	<PERSON> = 3,
	<PERSON>k = 4,
	<PERSON><PERSON> = 5,
	<PERSON> = 6,
	<PERSON> = 7,
	<PERSON> = 8,

	CopperOre = 9,
	IronOre = 10,
	GoldOre = 11,
	IndigosiumOre = 12,
	MithrilOre = 13,
	ErithrydiumOre = 14,
	AdamantiteOre = 15,
	UraniumOre = 16,

	CopperBar = 17,
	IronBar = 18,
	GoldBar = 19,
	IndigosiumBar = 20,
	MithrilBar = 21,
	ErithrydiumBar = 22,
	AdamantiteBar = 23,
	UraniumBar = 24,

	CopperSheet = 25,
	IronSheet = 26,
	GoldSheet = 27,
	IndigosiumSheet = 28,
	MithrilSheet = 29,
	ErithrydiumSheet = 30,
	AdamantiteSheet = 31,
	UraniumSheet = 32,

	WoodenBeam = 33,
	WoodenStick = 34,
	RawRabbitLeg = 35,
	CookedRabbitLeg = 36,
	<PERSON><PERSON><PERSON><PERSON> = 37,
	Arrow = 38,
	CopperKey = 39,
	<PERSON><PERSON>ey = 40,
	<PERSON><PERSON><PERSON> = 41,
	Indigosium<PERSON><PERSON> = 42,
	<PERSON><PERSON><PERSON><PERSON><PERSON> = 43,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> = 44,
	AdamantiteKey = 45,
	UraniumKey = 46,
	Charcoal = 47
}
