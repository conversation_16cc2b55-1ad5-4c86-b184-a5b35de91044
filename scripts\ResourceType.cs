using Godot;

[System.Serializable]
public enum ResourceType
{
	None = 0,
	<PERSON> = 1,
	<PERSON> = 2,
	<PERSON> = 3,
	<PERSON>k = 4,
	<PERSON><PERSON> = 5,
	<PERSON> = 6,
	<PERSON> = 7,

	CopperOre = 8,
	IronOre = 9,
	GoldOre = 10,
	IndigosiumOre = 11,
	MithrilOre = 12,
	E<PERSON>rydiumOre = 13,
	AdamantiteOre = 14,
	UraniumOre = 15,

	CopperBar = 16,
	IronBar = 17,
	GoldBar = 18,
	IndigosiumBar = 19,
	MithrilBar = 20,
	ErithrydiumBar = 21,
	AdamantiteBar = 22,
	UraniumBar = 23,

	CopperSheet = 24,
	IronSheet = 25,
	GoldSheet = 26,
	IndigosiumSheet = 27,
	MithrilSheet = 28,
	ErithrydiumSheet = 29,
	AdamantiteSheet = 30,
	UraniumSheet = 31,

	WoodenBeam = 32,
	WoodenStick = 33,
	RawRabbitLeg = 34,
	CookedRabbitLeg = 35,
	<PERSON><PERSON><PERSON><PERSON> = 36,
	<PERSON> = 37,
	<PERSON>Key = 38,
	<PERSON>Key = 39,
	<PERSON><PERSON><PERSON> = 40,
	Indigosium<PERSON><PERSON> = 41,
	<PERSON><PERSON><PERSON><PERSON><PERSON> = 42,
	E<PERSON>rydiumKey = 43,
	Adamantite<PERSON>ey = 44,
	UraniumKey = 45,
	<PERSON>rcoal = 46
}
