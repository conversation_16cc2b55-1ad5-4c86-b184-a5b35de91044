using Godot;

public partial class BuildMenu : CanvasLayer
{
	private AnimationPlayer _animationPlayer;
	private Button _closeButton;
	private Button _anvilBuildButton;
	private Button _bridgeBuildButton;
	private Button _campfireBuildButton;
	private Button _furnace1BuildButton;
	private Button _furnace2BuildButton;
	private Button _furnace3BuildButton;
	private Button _furnace4BuildButton;
	private Sprite2D _anvilBuildButtonSprite;
	private Sprite2D _bridgeBuildButtonSprite;
	private Sprite2D _campfireBuildButtonSprite;
	private Sprite2D _furnace1BuildButtonSprite;
	private Sprite2D _furnace2BuildButtonSprite;
	private Sprite2D _furnace3BuildButtonSprite;
	private Sprite2D _furnace4BuildButtonSprite;

	// Anvil building requirements
	private const int ANVIL_WOOD_REQUIRED = 5;
	private const int ANVIL_STONE_REQUIRED = 5;

	// Bridge building requirements
	private const int BRIDGE_PLANK_REQUIRED = 5;
	private const int BRIDGE_BEAM_REQUIRED = 2;

	// Campfire building requirements
	private const int CAMPFIRE_WOOD_REQUIRED = 5;
	private const int CAMPFIRE_STONE_REQUIRED = 2;

	// Furnace1 building requirements
	private const int FURNACE1_COPPERORE_REQUIRED = 10;
	private const int FURNACE1_IRONORE_REQUIRED = 3;

	// Furnace2 building requirements
	private const int FURNACE2_GOLDORE_REQUIRED = 10;
	private const int FURNACE2_INDIGOSIUMORE_REQUIRED = 3;

	// Furnace3 building requirements
	private const int FURNACE3_MITHRILORE_REQUIRED = 10;
	private const int FURNACE3_ERITHRYDIUMORE_REQUIRED = 3;

	// Furnace4 building requirements
	private const int FURNACE4_ADAMANTITEORE_REQUIRED = 10;
	private const int FURNACE4_URANIUMORE_REQUIRED = 3;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		if (_animationPlayer == null)
		{
			GD.PrintErr("BuildMenu: AnimationPlayer node not found!");
			return;
		}

		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		if (_closeButton == null)
		{
			GD.PrintErr("BuildMenu: CloseButton node not found!");
			return;
		}

		_anvilBuildButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListAnvil/Button");
		if (_anvilBuildButton == null)
		{
			GD.PrintErr("BuildMenu: Anvil build button not found!");
			return;
		}

		_bridgeBuildButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListBridge/Button");
		if (_bridgeBuildButton == null)
		{
			GD.PrintErr("BuildMenu: Bridge build button not found!");
			return;
		}

		_campfireBuildButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCampfire/Button");
		if (_campfireBuildButton == null)
		{
			GD.PrintErr("BuildMenu: Campfire build button not found!");
			return;
		}

		_anvilBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListAnvil/BuildButton");
		if (_anvilBuildButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Anvil BuildButton sprite not found!");
			return;
		}

		_bridgeBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListBridge/BuildButton");
		if (_bridgeBuildButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Bridge BuildButton sprite not found!");
			return;
		}

		_campfireBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCampfire/BuildButton");
		if (_campfireBuildButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Campfire BuildButton sprite not found!");
			return;
		}

		_furnace1BuildButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace1/Button");
		if (_furnace1BuildButton == null)
		{
			GD.PrintErr("BuildMenu: Furnace1 build button not found!");
			return;
		}

		_furnace1BuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace1/BuildButton");
		if (_furnace1BuildButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Furnace1 BuildButton sprite not found!");
			return;
		}

		_furnace2BuildButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace2/Button");
		if (_furnace2BuildButton == null)
		{
			GD.PrintErr("BuildMenu: Furnace2 build button not found!");
			return;
		}

		_furnace2BuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace2/BuildButton");
		if (_furnace2BuildButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Furnace2 BuildButton sprite not found!");
			return;
		}

		_furnace3BuildButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace3/Button");
		if (_furnace3BuildButton == null)
		{
			GD.PrintErr("BuildMenu: Furnace3 build button not found!");
			return;
		}

		_furnace3BuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace3/BuildButton");
		if (_furnace3BuildButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Furnace3 BuildButton sprite not found!");
			return;
		}

		_furnace4BuildButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace4/Button");
		if (_furnace4BuildButton == null)
		{
			GD.PrintErr("BuildMenu: Furnace4 build button not found!");
			return;
		}

		_furnace4BuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace4/BuildButton");
		if (_furnace4BuildButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Furnace4 BuildButton sprite not found!");
			return;
		}

		_closeButton.Pressed += OnCloseButtonPressed;
		_anvilBuildButton.Pressed += OnAnvilBuildButtonPressed;
		_bridgeBuildButton.Pressed += OnBridgeBuildButtonPressed;
		_campfireBuildButton.Pressed += OnCampfireBuildButtonPressed;
		_furnace1BuildButton.Pressed += OnFurnace1BuildButtonPressed;
		_furnace2BuildButton.Pressed += OnFurnace2BuildButtonPressed;
		_furnace3BuildButton.Pressed += OnFurnace3BuildButtonPressed;
		_furnace4BuildButton.Pressed += OnFurnace4BuildButtonPressed;

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("RESET");
		}
	}

	public void OpenMenu()
	{
		// Update button states based on available resources
		UpdateBuildButtonStates();

		// Disable player movement when build menu opens
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("Open");
		}
	}

	public void CloseMenu()
	{
		// Re-enable player movement when build menu closes
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("Close");
		}
	}

	private void OnCloseButtonPressed()
	{
		CloseMenu();
	}

	private void OnAnvilBuildButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordAnvil())
		{
			GD.Print("BuildMenu: Not enough resources to build anvil!");
			return;
		}

		CloseMenu();

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingAnvil();
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnBridgeBuildButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordBridge())
		{
			GD.Print("BuildMenu: Not enough resources to build bridge!");
			return;
		}

		CloseMenu();

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingBridge();
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnCampfireBuildButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordCampfire())
		{
			GD.Print("BuildMenu: Not enough resources to build campfire!");
			return;
		}

		CloseMenu();

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingCampfire();
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnFurnace1BuildButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordFurnace1())
		{
			GD.Print("BuildMenu: Not enough resources to build furnace1!");
			return;
		}

		CloseMenu();

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingFurnace1();
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnFurnace2BuildButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordFurnace2())
		{
			GD.Print("BuildMenu: Not enough resources to build furnace2!");
			return;
		}

		CloseMenu();

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingFurnace2();
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnFurnace3BuildButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordFurnace3())
		{
			GD.Print("BuildMenu: Not enough resources to build furnace3!");
			return;
		}

		CloseMenu();

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingFurnace3();
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnFurnace4BuildButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordFurnace4())
		{
			GD.Print("BuildMenu: Not enough resources to build furnace4!");
			return;
		}

		CloseMenu();

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingFurnace4();
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	/// <summary>
	/// Check if player has enough resources to build an anvil
	/// </summary>
	private bool CanAffordAnvil()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		bool hasWood = resourcesManager.HasResource(ResourceType.Wood, ANVIL_WOOD_REQUIRED);
		bool hasStone = resourcesManager.HasResource(ResourceType.Stone, ANVIL_STONE_REQUIRED);

		return hasWood && hasStone;
	}

	/// <summary>
	/// Check if player has enough resources to build a bridge
	/// </summary>
	private bool CanAffordBridge()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		bool hasPlank = resourcesManager.HasResource(ResourceType.Plank, BRIDGE_PLANK_REQUIRED);
		bool hasBeam = resourcesManager.HasResource(ResourceType.WoodenBeam, BRIDGE_BEAM_REQUIRED);

		return hasPlank && hasBeam;
	}

	/// <summary>
	/// Update the build button visual states based on available resources
	/// </summary>
	private void UpdateBuildButtonStates()
	{
		// Update anvil button
		bool canAffordAnvil = CanAffordAnvil();
		if (_anvilBuildButtonSprite != null)
		{
			if (canAffordAnvil)
			{
				_anvilBuildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_anvilBuildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_anvilBuildButton != null)
		{
			_anvilBuildButton.Disabled = !canAffordAnvil;
		}

		// Update bridge button
		bool canAffordBridge = CanAffordBridge();
		if (_bridgeBuildButtonSprite != null)
		{
			if (canAffordBridge)
			{
				_bridgeBuildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_bridgeBuildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_bridgeBuildButton != null)
		{
			_bridgeBuildButton.Disabled = !canAffordBridge;
		}

		// Update campfire button
		bool canAffordCampfire = CanAffordCampfire();
		if (_campfireBuildButtonSprite != null)
		{
			if (canAffordCampfire)
			{
				_campfireBuildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_campfireBuildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_campfireBuildButton != null)
		{
			_campfireBuildButton.Disabled = !canAffordCampfire;
		}

		// Update furnace1 button
		bool canAffordFurnace1 = CanAffordFurnace1();
		if (_furnace1BuildButtonSprite != null)
		{
			if (canAffordFurnace1)
			{
				_furnace1BuildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_furnace1BuildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_furnace1BuildButton != null)
		{
			_furnace1BuildButton.Disabled = !canAffordFurnace1;
		}

		// Update furnace2 button
		bool canAffordFurnace2 = CanAffordFurnace2();
		if (_furnace2BuildButtonSprite != null)
		{
			if (canAffordFurnace2)
			{
				_furnace2BuildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_furnace2BuildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_furnace2BuildButton != null)
		{
			_furnace2BuildButton.Disabled = !canAffordFurnace2;
		}

		// Update furnace3 button
		bool canAffordFurnace3 = CanAffordFurnace3();
		if (_furnace3BuildButtonSprite != null)
		{
			if (canAffordFurnace3)
			{
				_furnace3BuildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_furnace3BuildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_furnace3BuildButton != null)
		{
			_furnace3BuildButton.Disabled = !canAffordFurnace3;
		}

		// Update furnace4 button
		bool canAffordFurnace4 = CanAffordFurnace4();
		if (_furnace4BuildButtonSprite != null)
		{
			if (canAffordFurnace4)
			{
				_furnace4BuildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_furnace4BuildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_furnace4BuildButton != null)
		{
			_furnace4BuildButton.Disabled = !canAffordFurnace4;
		}
	}

	public override void _ExitTree()
	{
		if (_closeButton != null)
		{
			_closeButton.Pressed -= OnCloseButtonPressed;
		}
		if (_anvilBuildButton != null)
		{
			_anvilBuildButton.Pressed -= OnAnvilBuildButtonPressed;
		}
		if (_bridgeBuildButton != null)
		{
			_bridgeBuildButton.Pressed -= OnBridgeBuildButtonPressed;
		}
		if (_campfireBuildButton != null)
		{
			_campfireBuildButton.Pressed -= OnCampfireBuildButtonPressed;
		}
		if (_furnace1BuildButton != null)
		{
			_furnace1BuildButton.Pressed -= OnFurnace1BuildButtonPressed;
		}
		if (_furnace2BuildButton != null)
		{
			_furnace2BuildButton.Pressed -= OnFurnace2BuildButtonPressed;
		}
		if (_furnace3BuildButton != null)
		{
			_furnace3BuildButton.Pressed -= OnFurnace3BuildButtonPressed;
		}
		if (_furnace4BuildButton != null)
		{
			_furnace4BuildButton.Pressed -= OnFurnace4BuildButtonPressed;
		}
	}

	private bool CanAffordCampfire()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.Wood, CAMPFIRE_WOOD_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.Stone, CAMPFIRE_STONE_REQUIRED);
	}

	private bool CanAffordFurnace1()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.CopperOre, FURNACE1_COPPERORE_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.IronOre, FURNACE1_IRONORE_REQUIRED);
	}

	private bool CanAffordFurnace2()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.GoldOre, FURNACE2_GOLDORE_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.IndigosiumOre, FURNACE2_INDIGOSIUMORE_REQUIRED);
	}

	private bool CanAffordFurnace3()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.MithrilOre, FURNACE3_MITHRILORE_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.ErithrydiumOre, FURNACE3_ERITHRYDIUMORE_REQUIRED);
	}

	private bool CanAffordFurnace4()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.AdamantiteOre, FURNACE4_ADAMANTITEORE_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.UraniumOre, FURNACE4_URANIUMORE_REQUIRED);
	}
}
