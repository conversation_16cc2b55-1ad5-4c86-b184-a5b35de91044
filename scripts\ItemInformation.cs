using Godot;
using System.Collections.Generic;

public enum DescriptionType
{
    TextOnly
}

public class ItemInfo
{
    public string Title { get; set; }
    public string Description { get; set; }
    public DescriptionType DescriptionType { get; set; }
    public bool CanBeUsed { get; set; }
    public bool CanAssignToQuickUse { get; set; }
    public int SellPrice { get; set; } = 0;
    public int BuyPrice { get; set; } = 0;
}

public static class ItemInformation
{
    private static readonly Dictionary<ResourceType, ItemInfo> _resourceInfo = new Dictionary<ResourceType, ItemInfo>
    {
        {
            ResourceType.Wood,
            new ItemInfo
            {
                Title = "TEXT_WOOD_TITLE",
                Description = "TEXT_WOOD_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 1,
                BuyPrice = 2
            }
        },
        {
            ResourceType.Stone,
            new ItemInfo
            {
                Title = "TEXT_STONE_TITLE",
                Description = "TEXT_STONE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 1,
                BuyPrice = 2
            }
        },
        {
            ResourceType.Net,
            new ItemInfo
            {
                Title = "TEXT_NET_TITLE",
                Description = "TEXT_NET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = false,
                SellPrice = 5,
                BuyPrice = 10
            }
        },
        {
            ResourceType.Plank,
            new ItemInfo
            {
                Title = "PLANK_TEXT",
                Description = "PLANK_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.Stone2,
            new ItemInfo
            {
                Title = "STONE2_TEXT",
                Description = "STONE2_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.Berry,
            new ItemInfo
            {
                Title = "BERRY_TEXT",
                Description = "BERRY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.Leaf,
            new ItemInfo
            {
                Title = "LEAF_TEXT",
                Description = "LEAF_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 1,
                BuyPrice = 2
            }
        },
        {
            ResourceType.CopperOre,
            new ItemInfo
            {
                Title = "COPPER_ORE_TEXT",
                Description = "COPPER_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.IronOre,
            new ItemInfo
            {
                Title = "IRON_ORE_TEXT",
                Description = "IRON_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 4,
                BuyPrice = 8
            }
        },
        {
            ResourceType.GoldOre,
            new ItemInfo
            {
                Title = "GOLD_ORE_TEXT",
                Description = "GOLD_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 8,
                BuyPrice = 16
            }
        },
        {
            ResourceType.IndigosiumOre,
            new ItemInfo
            {
                Title = "INDIGOSIUM_ORE_TEXT",
                Description = "INDIGOSIUM_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 12,
                BuyPrice = 24
            }
        },
        {
            ResourceType.MithrilOre,
            new ItemInfo
            {
                Title = "MITHRIL_ORE_TEXT",
                Description = "MITHRIL_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 16,
                BuyPrice = 32
            }
        },
        {
            ResourceType.ErithrydiumOre,
            new ItemInfo
            {
                Title = "ERITHRYDIUM_ORE_TEXT",
                Description = "ERITHRYDIUM_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 20,
                BuyPrice = 40
            }
        },
        {
            ResourceType.AdamantiteOre,
            new ItemInfo
            {
                Title = "ADAMANTITE_ORE_TEXT",
                Description = "ADAMANTITE_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 25,
                BuyPrice = 50
            }
        },
        {
            ResourceType.UraniumOre,
            new ItemInfo
            {
                Title = "URANIUM_ORE_TEXT",
                Description = "URANIUM_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 30,
                BuyPrice = 60
            }
        },
        {
            ResourceType.CopperBar,
            new ItemInfo
            {
                Title = "COPPER_BAR_TEXT",
                Description = "COPPER_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 6,
                BuyPrice = 12
            }
        },
        {
            ResourceType.IronBar,
            new ItemInfo
            {
                Title = "IRON_BAR_TEXT",
                Description = "IRON_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 8,
                BuyPrice = 16
            }
        },
        {
            ResourceType.GoldBar,
            new ItemInfo
            {
                Title = "GOLD_BAR_TEXT",
                Description = "GOLD_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 16,
                BuyPrice = 32
            }
        },
        {
            ResourceType.IndigosiumBar,
            new ItemInfo
            {
                Title = "INDIGOSIUM_BAR_TEXT",
                Description = "INDIGOSIUM_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 24,
                BuyPrice = 48
            }
        },
        {
            ResourceType.MithrilBar,
            new ItemInfo
            {
                Title = "MITHRIL_BAR_TEXT",
                Description = "MITHRIL_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 32,
                BuyPrice = 64
            }
        },
        {
            ResourceType.ErithrydiumBar,
            new ItemInfo
            {
                Title = "ERITHRYDIUM_BAR_TEXT",
                Description = "ERITHRYDIUM_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 40,
                BuyPrice = 80
            }
        },
        {
            ResourceType.AdamantiteBar,
            new ItemInfo
            {
                Title = "ADAMANTITE_BAR_TEXT",
                Description = "ADAMANTITE_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 50,
                BuyPrice = 100
            }
        },
        {
            ResourceType.UraniumBar,
            new ItemInfo
            {
                Title = "URANIUM_BAR_TEXT",
                Description = "URANIUM_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 60,
                BuyPrice = 120
            }
        },
        {
            ResourceType.CopperSheet,
            new ItemInfo
            {
                Title = "COPPER_SHEET_TEXT",
                Description = "COPPER_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 12,
                BuyPrice = 24
            }
        },
        {
            ResourceType.IronSheet,
            new ItemInfo
            {
                Title = "IRON_SHEET_TEXT",
                Description = "IRON_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 16,
                BuyPrice = 32
            }
        },
        {
            ResourceType.GoldSheet,
            new ItemInfo
            {
                Title = "GOLD_SHEET_TEXT",
                Description = "GOLD_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 32,
                BuyPrice = 64
            }
        },
        {
            ResourceType.IndigosiumSheet,
            new ItemInfo
            {
                Title = "INDIGOSIUM_SHEET_TEXT",
                Description = "INDIGOSIUM_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 48,
                BuyPrice = 96
            }
        },
        {
            ResourceType.MithrilSheet,
            new ItemInfo
            {
                Title = "MITHRIL_SHEET_TEXT",
                Description = "MITHRIL_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 64,
                BuyPrice = 128
            }
        },
        {
            ResourceType.ErithrydiumSheet,
            new ItemInfo
            {
                Title = "ERITHRYDIUM_SHEET_TEXT",
                Description = "ERITHRYDIUM_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 80,
                BuyPrice = 160
            }
        },
        {
            ResourceType.AdamantiteSheet,
            new ItemInfo
            {
                Title = "ADAMANTITE_SHEET_TEXT",
                Description = "ADAMANTITE_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 100,
                BuyPrice = 200
            }
        },
        {
            ResourceType.UraniumSheet,
            new ItemInfo
            {
                Title = "URANIUM_SHEET_TEXT",
                Description = "URANIUM_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 120,
                BuyPrice = 240
            }
        },
        {
            ResourceType.WoodenBeam,
            new ItemInfo
            {
                Title = "WOODEN_BEAM_TEXT",
                Description = "WOODEN_BEAM_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 4,
                BuyPrice = 8
            }
        },
        {
            ResourceType.WoodenStick,
            new ItemInfo
            {
                Title = "STICK_TEXT",
                Description = "STICK_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.RawRabbitLeg,
            new ItemInfo
            {
                Title = "RAW_RABBIT_LEG_TEXT",
                Description = "RAW_RABBIT_LEG_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 3,
                BuyPrice = 6
            }
        },
        {
            ResourceType.CookedRabbitLeg,
            new ItemInfo
            {
                Title = "COOKED_RABBIT_LEG_TEXT",
                Description = "COOKED_RABBIT_LEG_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 8,
                BuyPrice = 16
            }
        },
        {
            ResourceType.WoodenKey,
            new ItemInfo
            {
                Title = "WOODEN_KEY_TEXT",
                Description = "WOODEN_KEY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 10,
                BuyPrice = 20
            }
        },
        {
            ResourceType.Arrow,
            new ItemInfo
            {
                Title = "TEXT_ARROW_TITLE",
                Description = "TEXT_ARROW_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 2,
                BuyPrice = 4
            }
        },
        {
            ResourceType.CopperKey,
            new ItemInfo
            {
                Title = "COPPER_KEY_TEXT",
                Description = "COPPER_KEY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 15,
                BuyPrice = 30
            }
        },
        {
            ResourceType.IronKey,
            new ItemInfo
            {
                Title = "IRON_KEY_TEXT",
                Description = "IRON_KEY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 20,
                BuyPrice = 40
            }
        },
        {
            ResourceType.GoldKey,
            new ItemInfo
            {
                Title = "GOLD_KEY_TEXT",
                Description = "GOLD_KEY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 30,
                BuyPrice = 60
            }
        },
        {
            ResourceType.IndigosiumKey,
            new ItemInfo
            {
                Title = "INDIGOSIUM_KEY_TEXT",
                Description = "INDIGOSIUM_KEY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 40,
                BuyPrice = 80
            }
        },
        {
            ResourceType.MithrilKey,
            new ItemInfo
            {
                Title = "MITHRIL_KEY_TEXT",
                Description = "MITHRIL_KEY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 50,
                BuyPrice = 100
            }
        },
        {
            ResourceType.ErithrydiumKey,
            new ItemInfo
            {
                Title = "ERITHRYDIUM_KEY_TEXT",
                Description = "ERITHRYDIUM_KEY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 60,
                BuyPrice = 120
            }
        },
        {
            ResourceType.AdamantiteKey,
            new ItemInfo
            {
                Title = "ADAMANTITE_KEY_TEXT",
                Description = "ADAMANTITE_KEY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 70,
                BuyPrice = 140
            }
        },
        {
            ResourceType.UraniumKey,
            new ItemInfo
            {
                Title = "URANIUM_KEY_TEXT",
                Description = "URANIUM_KEY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true,
                SellPrice = 80,
                BuyPrice = 160
            }
        },
        {
            ResourceType.Charcoal,
            new ItemInfo
            {
                Title = "CHARCOAL_TEXT",
                Description = "CHARCOAL_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = false,
                SellPrice = 3,
                BuyPrice = 6
            }
        }
    };

    private static readonly Dictionary<ToolType, ItemInfo> _toolInfo = new Dictionary<ToolType, ItemInfo>
    {
        {
            ToolType.Pickaxe,
            new ItemInfo
            {
                Title = "TEXT_PICKAXE_TITLE",
                Description = "TEXT_PICKAXE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ToolType.Sword,
            new ItemInfo
            {
                Title = "TEXT_SWORD_TITLE",
                Description = "TEXT_SWORD_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ToolType.Bow,
            new ItemInfo
            {
                Title = "TEXT_BOW_TITLE",
                Description = "TEXT_BOW_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ToolType.Hammer,
            new ItemInfo
            {
                Title = "TEXT_HAMMER_TITLE",
                Description = "TEXT_HAMMER_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ToolType.Hoe,
            new ItemInfo
            {
                Title = "TEXT_HOE_TITLE",
                Description = "TEXT_HOE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ToolType.WateringCan,
            new ItemInfo
            {
                Title = "TEXT_WATERING_CAN_TITLE",
                Description = "TEXT_WATERING_CAN_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        }
    };

    public static ItemInfo GetResourceInfo(ResourceType resourceType)
    {
        return _resourceInfo.TryGetValue(resourceType, out var info) ? info : new ItemInfo
        {
            Title = "TEXT_UNKNOWN_ITEM_TITLE",
            Description = "TEXT_UNKNOWN_ITEM_DESCRIPTION",
            DescriptionType = DescriptionType.TextOnly,
            CanBeUsed = false,
            CanAssignToQuickUse = false
        };
    }

    public static ItemInfo GetToolInfo(ToolType toolType)
    {
        return _toolInfo.TryGetValue(toolType, out var info) ? info : new ItemInfo
        {
            Title = "TEXT_UNKNOWN_TOOL_TITLE",
            Description = "TEXT_UNKNOWN_TOOL_DESCRIPTION",
            DescriptionType = DescriptionType.TextOnly,
            CanBeUsed = false,
            CanAssignToQuickUse = false
        };
    }

    public static bool CanBeUsed(ResourceType resourceType)
    {
        return GetResourceInfo(resourceType).CanBeUsed;
    }

    public static bool CanBeUsed(ToolType toolType)
    {
        return GetToolInfo(toolType).CanBeUsed;
    }

    public static bool CanAssignToQuickUse(ResourceType resourceType)
    {
        return GetResourceInfo(resourceType).CanAssignToQuickUse;
    }

    public static bool CanAssignToQuickUse(ToolType toolType)
    {
        return GetToolInfo(toolType).CanAssignToQuickUse;
    }
}
